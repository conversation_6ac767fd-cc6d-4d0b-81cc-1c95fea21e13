/* Markdown Renderer Styles */
.markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Headings */
.markdown-heading {
  margin: 1.5em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
}

.markdown-heading:first-child {
  margin-top: 0;
}

.markdown-heading--1 {
  font-size: 2em;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.3em;
}

.markdown-heading--2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.3em;
}

.markdown-heading--3 {
  font-size: 1.25em;
}

.markdown-heading--4 {
  font-size: 1em;
}

.markdown-heading--5 {
  font-size: 0.875em;
}

.markdown-heading--6 {
  font-size: 0.85em;
  color: var(--text-secondary);
}

/* Paragraphs */
.markdown-paragraph {
  margin: 0 0 1em 0;
  line-height: 1.6;
}

.markdown-paragraph:last-child {
  margin-bottom: 0;
}

/* Text formatting */
.markdown-strong {
  font-weight: 600;
}

.markdown-emphasis {
  font-style: italic;
}

/* Links */
.markdown-link {
  color: var(--accent-color, #007acc);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  border-bottom-color: var(--accent-color, #007acc);
  text-decoration: none;
}

/* Code */
.markdown-inline-code {
  background: var(--code-bg, rgba(175, 184, 193, 0.2));
  color: var(--code-color, #e83e8c);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85em;
}

.markdown-code-block {
  margin: 1em 0;
  border-radius: 6px;
  overflow: hidden;
  background: var(--code-block-bg, #f6f8fa);
  border: 1px solid var(--border-color);
}

.markdown-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em 1em;
  background: var(--code-header-bg, #e1e4e8);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.75em;
}

.markdown-code-language {
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.markdown-code-copy {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.25em 0.5em;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.75em;
  transition: all 0.2s ease;
}

.markdown-code-copy:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.markdown-code-pre {
  margin: 0;
  padding: 1em;
  overflow-x: auto;
  background: transparent;
}

.markdown-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85em;
  line-height: 1.45;
  color: var(--text-primary);
  background: transparent;
}

/* Lists */
.markdown-list {
  margin: 0.5em 0 1em 0;
  padding-left: 2em;
}

.markdown-list--unordered {
  list-style-type: disc;
}

.markdown-list--ordered {
  list-style-type: decimal;
}

.markdown-list li {
  margin: 0.25em 0;
  line-height: 1.5;
}

.markdown-list li > .markdown-list {
  margin: 0.25em 0;
}

/* Blockquotes */
.markdown-blockquote {
  margin: 1em 0;
  padding: 0 1em;
  border-left: 4px solid var(--accent-color, #007acc);
  background: var(--blockquote-bg, rgba(0, 122, 204, 0.05));
  color: var(--text-secondary);
  font-style: italic;
}

.markdown-blockquote p {
  margin: 0.5em 0;
}

/* Tables */
.markdown-table-wrapper {
  margin: 1em 0;
  overflow-x: auto;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

.markdown-table th,
.markdown-table td {
  padding: 0.75em 1em;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.markdown-table th {
  background: var(--table-header-bg, #f6f8fa);
  font-weight: 600;
  color: var(--text-primary);
}

.markdown-table tr:last-child td {
  border-bottom: none;
}

.markdown-table tr:nth-child(even) {
  background: var(--table-row-bg, rgba(0, 0, 0, 0.02));
}

/* Math/LaTeX */
.katex {
  font-size: 1em;
}

.katex-display {
  margin: 1em 0;
  text-align: center;
}

.katex-display > .katex {
  display: inline-block;
  text-align: initial;
}

/* Horizontal rules */
.markdown-renderer hr {
  margin: 2em 0;
  border: none;
  border-top: 1px solid var(--border-color);
}

/* Images */
.markdown-renderer img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 0.5em 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .markdown-heading--1 {
    font-size: 1.75em;
  }
  
  .markdown-heading--2 {
    font-size: 1.5em;
  }
  
  .markdown-code-block {
    margin: 0.5em -12px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .markdown-code-pre {
    padding: 0.75em;
  }
  
  .markdown-table-wrapper {
    margin: 1em -12px;
  }
  
  .markdown-list {
    padding-left: 1.5em;
  }
}
